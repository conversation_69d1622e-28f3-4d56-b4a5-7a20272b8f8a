---
trigger: always_on
---


Don't hold back.
Give it your all.
1. Always chat in English.
2. Add function-level comments  when generating code. And add docstring:
For example:
 r"""Represents an installation of a Discord application in a
        specific guild (server).

    Attributes:
        guild_id (str): The unique identifier for the Discord guild (server)
            where the application is installed.
        access_token (str): The access token used to authenticate API requests
            for the installed application.
        refresh_token (str): The token used to refresh the access token when
            it expires.
        installed_at (datetime): The timestamp indicating when the application
            was installed in the guild.
        token_expires_at (Optional[datetime]): The optional timestamp
            indicating when the access token will expire. Defaults to None
            if the token does not have an expiration time.
    """

3. My system is Mac.
4.each line don’t over 79 characters
5.when we wanna create a web application,please use nextjs(app router) + typescript + tailwindcss as frontend framework(using the staging to create the basic frontend,like yarn create next-app frontend --typescript --tailwind --eslint --app --src-dir --import-alias "@/*")
use the fastapi+postgresql as backend framework
6.using the uv to manage the virtual environment of python： reference guidance of uv
uv init example	Create and initialize a Python project
cd example	Enter the project directory
uv venv .venv --python=3.10 Create a virtual environment named .venv with Python 3.10
uv add ruff	Install ruff and write it to the dependency file
uv run ruff check	Execute Ruff's code checking function
uv lock	Generate a lock file to ensure consistent dependency versions
uv sync	Install all dependencies to keep the environment consistent


[[calls]]
match = "when the user requests code examples, setup or configuration steps, or library/API documentation"
tool  = "context7"

