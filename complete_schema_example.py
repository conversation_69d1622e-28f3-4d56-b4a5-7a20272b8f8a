#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的 OpenAI 工具模式生成示例
展示从 Python 函数到 OpenAI 工具模式的完整转换过程
"""

import json
from typing import List, Optional, Dict, Any, Union
from datetime import datetime


def create_user_profile(
    username: str,
    email: str,
    age: int,
    is_active: bool = True,
    tags: Optional[List[str]] = None,
    preferences: Optional[Dict[str, Any]] = None,
    subscription_type: Union[str, None] = "free"
) -> Dict[str, Any]:
    r"""创建新的用户配置文件。
    
    这个函数用于在系统中创建一个新的用户配置文件，包含用户的基本信息、
    偏好设置和订阅信息。所有必需的字段都必须提供，可选字段有合理的默认值。
    
    Args:
        username (str): 用户名，必须是唯一的标识符
        email (str): 用户的电子邮件地址，用于通信和验证
        age (int): 用户的年龄，必须是正整数
        is_active (bool): 用户账户是否激活，默认为 True
        tags (Optional[List[str]]): 用户标签列表，用于分类和搜索，
            默认为 None
        preferences (Optional[Dict[str, Any]]): 用户偏好设置字典，
            可以包含任意键值对，默认为 None
        subscription_type (Union[str, None]): 订阅类型，可以是字符串
            或 None，默认为 "free"
            
    Returns:
        Dict[str, Any]: 包含完整用户信息的字典，包括生成的用户ID
            和创建时间戳
            
    Raises:
        ValueError: 当年龄为负数或用户名为空时
        TypeError: 当邮箱格式不正确时
        
    Example:
        >>> profile = create_user_profile(
        ...     username="john_doe",
        ...     email="<EMAIL>", 
        ...     age=25,
        ...     tags=["developer", "python"]
        ... )
        >>> print(profile["username"])
        john_doe
    """
    # 输入验证
    if not username or not username.strip():
        raise ValueError("Username cannot be empty")
    
    if age < 0:
        raise ValueError("Age must be non-negative")
    
    if "@" not in email:
        raise TypeError("Invalid email format")
    
    # 设置默认值
    if tags is None:
        tags = []
    
    if preferences is None:
        preferences = {}
    
    # 创建用户配置文件
    profile = {
        "user_id": f"user_{hash(username) % 10000:04d}",
        "username": username,
        "email": email,
        "age": age,
        "is_active": is_active,
        "tags": tags,
        "preferences": preferences,
        "subscription_type": subscription_type,
        "created_at": datetime.now().isoformat(),
        "last_updated": datetime.now().isoformat()
    }
    
    return profile


def search_products(
    query: str,
    category: Optional[str] = None,
    min_price: Optional[float] = None,
    max_price: Optional[float] = None,
    in_stock: bool = True,
    sort_by: str = "relevance",
    limit: int = 10
) -> List[Dict[str, Any]]:
    r"""在产品数据库中搜索产品。
    
    Args:
        query (str): 搜索查询字符串
        category (Optional[str]): 产品类别过滤器，默认为 None（所有类别）
        min_price (Optional[float]): 最低价格过滤器，默认为 None
        max_price (Optional[float]): 最高价格过滤器，默认为 None  
        in_stock (bool): 是否只显示有库存的产品，默认为 True
        sort_by (str): 排序方式，可选值：'relevance', 'price', 'rating'，
            默认为 'relevance'
        limit (int): 返回结果的最大数量，默认为 10
        
    Returns:
        List[Dict[str, Any]]: 匹配的产品列表，每个产品包含详细信息
    """
    # 模拟搜索逻辑
    products = [
        {
            "id": 1,
            "name": f"Product matching '{query}'",
            "category": category or "general",
            "price": 29.99,
            "in_stock": in_stock,
            "rating": 4.5
        }
    ]
    
    return products[:limit]


def demonstrate_schema_generation():
    """演示模式生成过程"""
    
    print("=" * 80)
    print("OpenAI 工具模式自动生成演示")
    print("=" * 80)
    
    # 导入必要的函数（在实际环境中）
    try:
        from camel.toolkits.function_tool import get_openai_tool_schema, FunctionTool
        
        # 示例1: 复杂用户配置文件函数
        print("\n1. 复杂用户配置文件函数的模式生成:")
        print("-" * 50)
        
        user_schema = get_openai_tool_schema(create_user_profile)
        print("生成的工具模式:")
        print(json.dumps(user_schema, indent=2, ensure_ascii=False))
        
        # 创建工具并测试
        user_tool = FunctionTool(create_user_profile)
        print(f"\n工具名称: {user_tool.get_function_name()}")
        print(f"工具描述: {user_tool.get_function_description()[:100]}...")
        
        # 测试调用
        result = user_tool(
            username="test_user",
            email="<EMAIL>",
            age=25,
            tags=["developer", "python"]
        )
        print(f"\n调用结果: {result}")
        
        # 示例2: 产品搜索函数
        print("\n\n2. 产品搜索函数的模式生成:")
        print("-" * 50)
        
        search_schema = get_openai_tool_schema(search_products)
        print("生成的工具模式:")
        print(json.dumps(search_schema, indent=2, ensure_ascii=False))
        
        # 分析参数类型
        print("\n参数类型分析:")
        params = search_schema["function"]["parameters"]["properties"]
        for param_name, param_info in params.items():
            param_type = param_info.get("type", "unknown")
            description = param_info.get("description", "无描述")
            print(f"  {param_name}: {param_type} - {description}")
        
    except ImportError:
        print("无法导入 CAMEL 模块，显示模拟的模式结构...")
        
        # 显示预期的模式结构
        mock_schema = {
            "type": "function",
            "function": {
                "name": "create_user_profile",
                "description": "创建新的用户配置文件。\n这个函数用于在系统中创建一个新的用户配置文件...",
                "strict": True,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "username": {
                            "type": "string",
                            "description": "用户名，必须是唯一的标识符"
                        },
                        "email": {
                            "type": "string", 
                            "description": "用户的电子邮件地址，用于通信和验证"
                        },
                        "age": {
                            "type": "integer",
                            "description": "用户的年龄，必须是正整数"
                        },
                        "is_active": {
                            "type": ["boolean", "null"],
                            "description": "用户账户是否激活，默认为 True"
                        },
                        "tags": {
                            "type": ["array", "null"],
                            "items": {"type": "string"},
                            "description": "用户标签列表，用于分类和搜索，默认为 None"
                        },
                        "preferences": {
                            "type": ["object", "null"],
                            "additionalProperties": True,
                            "description": "用户偏好设置字典，可以包含任意键值对，默认为 None"
                        },
                        "subscription_type": {
                            "anyOf": [
                                {"type": "string"},
                                {"type": "null"}
                            ],
                            "description": "订阅类型，可以是字符串或 None，默认为 \"free\""
                        }
                    },
                    "required": ["username", "email", "age", "is_active", "tags", "preferences", "subscription_type"],
                    "additionalProperties": False
                }
            }
        }
        
        print("模拟的工具模式:")
        print(json.dumps(mock_schema, indent=2, ensure_ascii=False))


def analyze_key_features():
    """分析关键特性"""
    
    print("\n\n" + "=" * 80)
    print("关键特性分析")
    print("=" * 80)
    
    features = [
        ("类型注解支持", "自动识别 str, int, float, bool, List, Dict, Optional, Union 等类型"),
        ("默认值处理", "有默认值的参数自动标记为可选，类型变为 [原类型, 'null']"),
        ("文档字符串解析", "自动提取函数和参数描述，支持多种格式"),
        ("严格模式兼容", "生成的模式符合 OpenAI 严格模式要求"),
        ("嵌套类型支持", "支持复杂的嵌套数据结构"),
        ("验证和清理", "自动验证模式有效性并清理无用字段"),
    ]
    
    for i, (feature, description) in enumerate(features, 1):
        print(f"{i}. {feature}")
        print(f"   {description}")
        print()


if __name__ == "__main__":
    demonstrate_schema_generation()
    analyze_key_features()
