#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析自动从函数生成 OpenAI 工具模式的过程
"""

import json
import inspect
from typing import List, Optional, Dict, Any, Mapping
from inspect import Parameter, signature
from pydantic import BaseModel, create_model
from pydantic.fields import FieldInfo
from docstring_parser import parse

# 模拟 CAMEL 的相关函数（简化版本）
def to_pascal(snake_str: str) -> str:
    """将蛇形命名转换为帕斯卡命名"""
    return ''.join(word.capitalize() for word in snake_str.split('_'))


def get_pydantic_object_schema(model) -> Dict[str, Any]:
    """获取 Pydantic 模型的 JSON Schema"""
    return model.model_json_schema()


def demo_function(
    name: str,
    age: int,
    email: Optional[str] = None,
    tags: List[str] = None,
    metadata: Dict[str, Any] = None
) -> Dict[str, Any]:
    r"""创建用户配置文件。
    
    这个函数用于创建一个新的用户配置文件，包含基本信息和可选的元数据。
    
    Args:
        name (str): 用户的全名
        age (int): 用户的年龄，必须是正整数
        email (Optional[str]): 用户的电子邮件地址，可选
        tags (List[str]): 用户标签列表，默认为空列表
        metadata (Dict[str, Any]): 额外的元数据信息，默认为空字典
        
    Returns:
        Dict[str, Any]: 包含用户信息的字典
        
    Raises:
        ValueError: 当年龄为负数时抛出异常
    """
    if age < 0:
        raise ValueError("Age must be non-negative")
    
    if tags is None:
        tags = []
    if metadata is None:
        metadata = {}
        
    return {
        "name": name,
        "age": age,
        "email": email,
        "tags": tags,
        "metadata": metadata
    }


def step_by_step_schema_generation(func):
    """逐步演示模式生成过程"""
    
    print("=" * 80)
    print(f"逐步分析函数 '{func.__name__}' 的模式生成过程")
    print("=" * 80)
    
    # 步骤1: 解析函数签名
    print("\n步骤1: 解析函数签名")
    print("-" * 40)
    
    params: Mapping[str, Parameter] = signature(func).parameters
    print(f"函数签名: {signature(func)}")
    print(f"参数数量: {len(params)}")
    
    for param_name, param in params.items():
        print(f"  参数 '{param_name}':")
        print(f"    类型注解: {param.annotation}")
        print(f"    默认值: {param.default if param.default != Parameter.empty else '无'}")
        print(f"    参数类型: {param.kind}")
    
    # 步骤2: 构建 Pydantic 字段
    print("\n步骤2: 构建 Pydantic 字段")
    print("-" * 40)
    
    fields: Dict[str, tuple] = {}
    for param_name, p in params.items():
        param_type = p.annotation
        param_default = p.default
        param_kind = p.kind
        
        # 跳过可变参数
        if param_kind in (Parameter.VAR_POSITIONAL, Parameter.VAR_KEYWORD):
            print(f"  跳过可变参数: {param_name}")
            continue
            
        # 处理无类型注解的情况
        if param_type is Parameter.empty:
            param_type = Any
            print(f"  参数 '{param_name}' 无类型注解，使用 Any")
        
        # 处理默认值
        if param_default is Parameter.empty:
            fields[param_name] = (param_type, FieldInfo())
            print(f"  参数 '{param_name}': {param_type} (必需)")
        else:
            fields[param_name] = (param_type, FieldInfo(default=param_default))
            print(f"  参数 '{param_name}': {param_type} (默认值: {param_default})")
    
    # 步骤3: 创建 Pydantic 模型
    print("\n步骤3: 创建 Pydantic 模型")
    print("-" * 40)
    
    model_name = to_pascal(func.__name__)
    print(f"模型名称: {model_name}")
    
    model = create_model(model_name, **fields)
    print(f"创建的模型: {model}")
    
    # 步骤4: 生成 JSON Schema
    print("\n步骤4: 生成 JSON Schema")
    print("-" * 40)
    
    parameters_dict = get_pydantic_object_schema(model)
    print("原始 Pydantic Schema:")
    print(json.dumps(parameters_dict, indent=2, ensure_ascii=False))
    
    # 步骤5: 解析文档字符串
    print("\n步骤5: 解析文档字符串")
    print("-" * 40)
    
    docstring = parse(func.__doc__ or "")
    print(f"短描述: {docstring.short_description}")
    print(f"长描述: {docstring.long_description}")
    print("参数描述:")
    
    for param in docstring.params:
        print(f"  {param.arg_name}: {param.description}")
        # 将描述添加到 schema 中
        if param.arg_name in parameters_dict["properties"]:
            parameters_dict["properties"][param.arg_name]["description"] = param.description
    
    # 步骤6: 构建最终的 OpenAI 工具模式
    print("\n步骤6: 构建最终的 OpenAI 工具模式")
    print("-" * 40)
    
    # 组合函数描述
    short_description = docstring.short_description or ""
    long_description = docstring.long_description or ""
    if long_description:
        func_description = f"{short_description}\n{long_description}"
    else:
        func_description = short_description
    
    # 设置 OpenAI 严格模式要求
    parameters_dict["additionalProperties"] = False
    
    # 构建函数模式
    openai_function_schema = {
        "name": func.__name__,
        "description": func_description,
        "strict": True,
        "parameters": parameters_dict,
    }
    
    # 构建工具模式
    openai_tool_schema = {
        "type": "function",
        "function": openai_function_schema,
    }
    
    print("最终的 OpenAI 工具模式:")
    print(json.dumps(openai_tool_schema, indent=2, ensure_ascii=False))
    
    return openai_tool_schema


def analyze_type_mapping():
    """分析 Python 类型到 JSON Schema 类型的映射"""
    
    print("\n\n" + "=" * 80)
    print("Python 类型到 JSON Schema 类型映射分析")
    print("=" * 80)
    
    type_examples = [
        (str, "字符串类型"),
        (int, "整数类型"),
        (float, "浮点数类型"),
        (bool, "布尔类型"),
        (List[str], "字符串列表"),
        (Dict[str, Any], "字典类型"),
        (Optional[str], "可选字符串"),
    ]
    
    for python_type, description in type_examples:
        # 创建简单的 Pydantic 模型来查看类型映射
        TestModel = create_model('TestModel', field=(python_type, ...))
        schema = TestModel.model_json_schema()
        field_schema = schema['properties']['field']
        
        print(f"{description}:")
        print(f"  Python 类型: {python_type}")
        print(f"  JSON Schema: {json.dumps(field_schema, ensure_ascii=False)}")
        print()


if __name__ == "__main__":
    # 运行逐步分析
    schema = step_by_step_schema_generation(demo_function)
    
    # 分析类型映射
    analyze_type_mapping()
