#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示自动从函数生成 OpenAI 工具模式的过程
"""

import json
from typing import List, Optional, Dict, Any
from camel.toolkits.function_tool import (
    get_openai_tool_schema, 
    FunctionTool,
    get_openai_function_schema
)


def simple_calculator(a: int, b: int, operation: str = "add") -> float:
    r"""执行简单的数学计算。
    
    这个函数可以执行基本的数学运算，包括加法、减法、乘法和除法。
    
    Args:
        a (int): 第一个数字
        b (int): 第二个数字  
        operation (str): 要执行的运算类型，可选值：'add', 'subtract', 
            'multiply', 'divide'。默认为 'add'
            
    Returns:
        float: 计算结果
        
    Raises:
        ValueError: 当运算类型不支持时
        ZeroDivisionError: 当除法运算中除数为0时
    """
    if operation == "add":
        return float(a + b)
    elif operation == "subtract":
        return float(a - b)
    elif operation == "multiply":
        return float(a * b)
    elif operation == "divide":
        if b == 0:
            raise ZeroDivisionError("Cannot divide by zero")
        return float(a / b)
    else:
        raise ValueError(f"Unsupported operation: {operation}")


def search_database(
    query: str, 
    table_name: str,
    limit: Optional[int] = None,
    filters: Optional[Dict[str, Any]] = None
) -> List[Dict[str, Any]]:
    r"""在数据库中搜索数据。
    
    Args:
        query (str): 搜索查询字符串
        table_name (str): 要搜索的表名
        limit (Optional[int]): 限制返回结果的数量，默认为 None（无限制）
        filters (Optional[Dict[str, Any]]): 额外的过滤条件，默认为 None
        
    Returns:
        List[Dict[str, Any]]: 搜索结果列表，每个元素是一个字典
    """
    # 模拟数据库搜索
    results = [
        {"id": 1, "name": "示例数据", "query": query, "table": table_name}
    ]
    
    if limit:
        results = results[:limit]
        
    return results


def demo_schema_generation():
    """演示模式生成过程"""
    
    print("=" * 60)
    print("自动从函数生成 OpenAI 工具模式演示")
    print("=" * 60)
    
    # 示例1: 简单函数
    print("\n1. 简单计算器函数的模式生成:")
    print("-" * 40)
    
    # 生成工具模式
    calculator_schema = get_openai_tool_schema(simple_calculator)
    print("生成的完整工具模式:")
    print(json.dumps(calculator_schema, indent=2, ensure_ascii=False))
    
    # 提取函数部分
    function_schema = calculator_schema["function"]
    print(f"\n函数名: {function_schema['name']}")
    print(f"函数描述: {function_schema['description']}")
    print("参数定义:")
    for param_name, param_info in function_schema["parameters"]["properties"].items():
        print(f"  - {param_name}: {param_info}")
    
    # 示例2: 复杂函数
    print("\n\n2. 数据库搜索函数的模式生成:")
    print("-" * 40)
    
    search_schema = get_openai_tool_schema(search_database)
    print("生成的完整工具模式:")
    print(json.dumps(search_schema, indent=2, ensure_ascii=False))
    
    # 示例3: 使用 FunctionTool 包装
    print("\n\n3. 使用 FunctionTool 包装函数:")
    print("-" * 40)
    
    # 创建 FunctionTool 实例
    calculator_tool = FunctionTool(simple_calculator)
    search_tool = FunctionTool(search_database)
    
    print("计算器工具:")
    print(f"  名称: {calculator_tool.get_function_name()}")
    print(f"  描述: {calculator_tool.get_function_description()}")
    
    print("\n搜索工具:")
    print(f"  名称: {search_tool.get_function_name()}")
    print(f"  描述: {search_tool.get_function_description()}")
    
    # 示例4: 测试工具调用
    print("\n\n4. 测试工具调用:")
    print("-" * 40)
    
    # 调用计算器工具
    result1 = calculator_tool(10, 5, "multiply")
    print(f"计算器工具调用结果: 10 * 5 = {result1}")
    
    # 调用搜索工具
    result2 = search_tool("test query", "users", limit=1)
    print(f"搜索工具调用结果: {result2}")
    
    return calculator_schema, search_schema


def analyze_schema_structure(schema: Dict[str, Any]):
    """分析模式结构"""
    
    print("\n\n模式结构分析:")
    print("-" * 40)
    
    print("1. 顶层结构:")
    for key in schema.keys():
        print(f"   - {key}: {type(schema[key]).__name__}")
    
    function_info = schema["function"]
    print(f"\n2. 函数信息:")
    print(f"   - 名称: {function_info['name']}")
    print(f"   - 描述长度: {len(function_info['description'])} 字符")
    print(f"   - 严格模式: {function_info.get('strict', False)}")
    
    parameters = function_info["parameters"]
    print(f"\n3. 参数信息:")
    print(f"   - 参数类型: {parameters['type']}")
    print(f"   - 参数数量: {len(parameters['properties'])}")
    print(f"   - 必需参数: {parameters.get('required', [])}")
    print(f"   - 额外属性: {parameters.get('additionalProperties', True)}")
    
    print(f"\n4. 各参数详情:")
    for param_name, param_info in parameters["properties"].items():
        print(f"   - {param_name}:")
        print(f"     类型: {param_info.get('type', 'unknown')}")
        print(f"     描述: {param_info.get('description', 'N/A')}")
        if 'default' in param_info:
            print(f"     默认值: {param_info['default']}")


if __name__ == "__main__":
    # 运行演示
    calc_schema, search_schema = demo_schema_generation()
    
    # 分析第一个模式的结构
    analyze_schema_structure(calc_schema)
