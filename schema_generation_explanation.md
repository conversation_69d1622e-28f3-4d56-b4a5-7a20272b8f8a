# 自动从函数生成 OpenAI 工具模式详解

## 概述

CAMEL 框架能够自动将 Python 函数转换为 OpenAI API 兼容的工具模式（Tool Schema），这个过程涉及多个步骤的自动化处理。

## 核心流程

### 1. 函数签名解析

```python
def example_function(name: str, age: int, email: Optional[str] = None) -> Dict[str, Any]:
    """示例函数"""
    pass

# 解析结果：
# - name: str (必需参数)
# - age: int (必需参数)  
# - email: Optional[str] (可选参数，默认值 None)
```

**关键步骤：**
- 使用 `inspect.signature()` 获取函数签名
- 遍历每个参数，提取类型注解和默认值
- 跳过 `*args` 和 `**kwargs` 可变参数
- 无类型注解的参数默认为 `Any` 类型

### 2. Pydantic 模型创建

```python
# 将函数参数转换为 Pydantic 字段
fields = {
    'name': (str, FieldInfo()),                    # 必需字段
    'age': (int, FieldInfo()),                     # 必需字段
    'email': (Optional[str], FieldInfo(default=None))  # 可选字段
}

# 动态创建 Pydantic 模型
Model = create_model('ExampleFunction', **fields)
```

**作用：**
- 利用 Pydantic 的类型验证和 JSON Schema 生成能力
- 自动处理复杂类型（如 List、Dict、Optional 等）
- 生成符合 JSON Schema 规范的数据结构

### 3. JSON Schema 生成

```python
# Pydantic 模型自动生成的 JSON Schema
{
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "age": {"type": "integer"},
        "email": {"type": ["string", "null"]}
    },
    "required": ["name", "age"]
}
```

### 4. 文档字符串解析

```python
def example_function(name: str, age: int, email: Optional[str] = None):
    r"""创建用户配置文件。
    
    这个函数用于创建新的用户配置文件。
    
    Args:
        name (str): 用户的全名
        age (int): 用户的年龄
        email (Optional[str]): 电子邮件地址，可选
        
    Returns:
        Dict[str, Any]: 用户信息字典
    """
```

**解析结果：**
- 函数描述：短描述 + 长描述
- 参数描述：每个参数的详细说明
- 支持多种文档字符串格式（Google、NumPy、reST 等）

### 5. OpenAI 工具模式构建

```python
openai_tool_schema = {
    "type": "function",
    "function": {
        "name": "example_function",
        "description": "创建用户配置文件。\n这个函数用于创建新的用户配置文件。",
        "strict": True,
        "parameters": {
            "type": "object",
            "properties": {
                "name": {
                    "type": "string",
                    "description": "用户的全名"
                },
                "age": {
                    "type": "integer", 
                    "description": "用户的年龄"
                },
                "email": {
                    "type": ["string", "null"],
                    "description": "电子邮件地址，可选"
                }
            },
            "required": ["name", "age"],
            "additionalProperties": False
        }
    }
}
```

## 类型映射规则

| Python 类型 | JSON Schema 类型 | 说明 |
|-------------|------------------|------|
| `str` | `"string"` | 字符串 |
| `int` | `"integer"` | 整数 |
| `float` | `"number"` | 数字 |
| `bool` | `"boolean"` | 布尔值 |
| `List[T]` | `{"type": "array", "items": {...}}` | 数组 |
| `Dict[str, T]` | `{"type": "object", "additionalProperties": {...}}` | 对象 |
| `Optional[T]` | `["T的类型", "null"]` | 可空类型 |
| `Union[T1, T2]` | `{"anyOf": [...]}` | 联合类型 |

## 特殊处理

### 1. 严格模式支持
- 设置 `"strict": True` 启用 OpenAI 严格模式
- 添加 `"additionalProperties": False` 禁止额外属性
- 确保所有字段都有明确的类型定义

### 2. 可选参数处理
```python
# 有默认值的参数被处理为可选
def func(required: str, optional: str = "default"):
    pass

# 生成的 schema 中 optional 参数类型为 ["string", "null"]
```

### 3. 模式清理和验证
- 移除 Pydantic 生成的无用 `title` 字段
- 验证生成的模式符合 JSON Schema 规范
- 处理默认值和必需字段的关系

## 实际应用示例

```python
from camel.toolkits.function_tool import FunctionTool, get_openai_tool_schema

def calculate_area(length: float, width: float, shape: str = "rectangle") -> float:
    r"""计算图形面积。
    
    Args:
        length (float): 长度
        width (float): 宽度  
        shape (str): 图形类型，默认为 "rectangle"
        
    Returns:
        float: 计算得到的面积
    """
    if shape == "rectangle":
        return length * width
    elif shape == "triangle":
        return 0.5 * length * width
    else:
        raise ValueError(f"Unsupported shape: {shape}")

# 自动生成工具模式
tool_schema = get_openai_tool_schema(calculate_area)

# 创建函数工具
tool = FunctionTool(calculate_area)

# 调用工具
result = tool(10.0, 5.0, "rectangle")  # 返回 50.0
```

## 优势

1. **自动化**：无需手动编写 JSON Schema
2. **类型安全**：利用 Python 类型注解确保类型正确性
3. **文档集成**：自动提取函数文档作为描述信息
4. **标准兼容**：生成的模式完全符合 OpenAI API 规范
5. **灵活性**：支持复杂类型和可选参数

## 注意事项

1. **类型注解必需**：函数参数必须有类型注解
2. **文档字符串重要**：影响 AI 模型对工具的理解
3. **不支持可变参数**：`*args` 和 `**kwargs` 会被忽略
4. **复杂类型限制**：某些 Python 特有类型可能无法完美映射

这个自动化过程大大简化了将 Python 函数集成到 AI 系统中的工作，让开发者可以专注于业务逻辑而不是模式定义。
